FROM ubuntu:focal-20220302

ENV TERRAFORM_VERSION=1.8.3

# Required bash to support one-liner architecture checks (e.g. [[ ]]).
SHELL ["/bin/bash", "-c"]

RUN echo "System dependencies" && \
      apt-get -y update && \
      apt-get -y install \
        bash \
        curl \
        make \
        wget \
        unzip && \
    echo "Remove build dependencies" && \
      apt-get clean && \
      rm -rf /var/lib/apt/lists/*

RUN echo "Hashicorp dependencies" && \
    echo "Install terraform" && \
      cd /tmp && \
      [[ "$(uname -m)" == "aarch64" ]] && export ARCH=arm64 || export ARCH=amd64 && \
      echo "Installing architecture: ${ARCH}" && \
      wget -O /tmp/terraform.zip https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_${ARCH}.zip && \
      unzip /tmp/terraform.zip && \
      mv terraform /usr/bin && \
      rm -f /tmp/terraform.zip && \
    echo "Install tflint" && \
      curl -s https://raw.githubusercontent.com/terraform-linters/tflint/master/install_linux.sh | bash && \
    echo "Install tfsec" && \
      wget https://github.com/tfsec/tfsec/releases/download/v1.28.1/tfsec-linux-${ARCH} -O /usr/bin/tfsec && \
      chmod 0755 /usr/bin/tfsec

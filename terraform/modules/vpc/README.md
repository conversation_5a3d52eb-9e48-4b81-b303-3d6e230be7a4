# AWS VPC Module

This Terraform module creates a Virtual Private Cloud (VPC) in AWS.

## Usage

```hcl
module "vpc" {
  source = "../modules/vpc"

  # Specify input variables here, if any
}
```

## Inputs
- `vpc_name`: Name of VPC
- `vpc_cidr`: The IPv4 CIDR block for the VPC
- `public_subnet_count`: Number public subnets (<= count of AZs)
- `public_subnet_cidr`: List CIDR block of the desired public subnet (nums of item >= `public_subnet_count`)


## Outputs
- `vpc_id`: The ID of the VPC
- `public_subnet_ids`: List ID of the specific public subnet to retrieve
- `private_subnet_ids`: List ID of the specific private subnet to retrieve

# VPC
variable "vpc_name" {
  type        = string
  description = "Name of VPC"
}

variable "vpc_cidr" {
  type        = string
  description = "The IPv4 CIDR block for the VPC"
  default     = "10.0.0.0/16"
}

# Public Subnets
variable "public_subnet_count" {
  type        = number
  description = "Number of public subnets (<= count of AZs)"
  default     = 2
}

variable "public_subnet_cidr" {
  type        = list(string)
  description = "List CIDR block of the desired public subnet"
  default = [
    "10.0.0.0/24",
    "********/24"
  ]
}

# Private Subnets
variable "private_subnet_count" {
  type        = number
  description = "Number of private subnets (<= count of AZs)"
  default     = 2
}

variable "private_subnet_cidr" {
  type        = list(string)
  description = "List CIDR block of the desired private subnet (nums of items >= private_subnet_count)"
  default = [
    "********/24",
    "********/24"
  ]
}

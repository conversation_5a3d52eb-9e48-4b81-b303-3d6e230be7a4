# AWS Security Group Module

This Terraform module creates a Security Group in AWS.

## Usage

```hcl
module "security_group" {
  source = "../modules/security-group"

  # Specify input variables here, if any
}
```

## Inputs
- `ingress_ports`: List ports inbound
- `ingress_protocol`: Protocol inbound
- `egress_ports`: List ports outbound
- `egress_protocol`: Protocol outbound
- `vpc_id`: VPC ID
- `sg_name`: Name of the security group
- `egress_cidr_blocks`: List of CIDR blocks outbound
- `ingress_cidr_blocks`: List of CIDR blocks inbound
- `prefix_list_ids`: List of Prefix List IDs


## Outputs
- `security_group_id`: ID of the security group

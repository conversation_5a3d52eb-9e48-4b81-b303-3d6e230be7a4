variable "ingress_ports" {
  type        = list(string)
  description = "List ports inbound"
}

variable "ingress_protocol" {
  type        = string
  description = "Protocol inbound"
}

variable "egress_ports" {
  type        = list(string)
  description = "List ports outbound"
}

variable "egress_protocol" {
  type        = string
  description = "Protocol outbound"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID"
}

variable "sg_name" {
  type        = string
  description = "Name of the security group"
}

variable "egress_cidr_blocks" {
  type        = list(string)
  description = "List of CIDR blocks outbound"
}

variable "ingress_cidr_blocks" {
  type        = list(string)
  description = "List of CIDR blocks inbound"
}

variable "prefix_list_ids" {
  type        = list(string)
  default     = []
  description = "List of Prefix List IDs"
}

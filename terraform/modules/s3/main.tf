resource "aws_s3_bucket" "bucket" {
  bucket = var.bucket_name
  tags   = var.tags
}

resource "aws_s3_bucket_website_configuration" "website_config" {
  count  = var.website != null ? 1 : 0
  bucket = aws_s3_bucket.bucket.id

  index_document {
    suffix = var.website.index_document
  }

  error_document {
    key = var.website.error_document
  }

  routing_rule {
    condition {
      http_error_code_returned_equals = 404
    }
    redirect {
      replace_key_with = "index.html"
    }
  }
}

resource "aws_s3_bucket_ownership_controls" "bucket_ownership_controls" {
  bucket = aws_s3_bucket.bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.bucket_ownership_controls]

  bucket = aws_s3_bucket.bucket.id
  acl    = var.acl
}

# data "aws_iam_policy_document" "s3_policy" {
#   statement {
#     sid    = "PublicReadGetObject"
#     effect = "Allow"

#     principals {
#       type        = "*"
#       identifiers = ["*"]
#     }

#     actions   = ["s3:GetObject"]
#     resources = ["${aws_s3_bucket.bucket.arn}/*"]
#   }
# }

# resource "aws_s3_bucket_policy" "bucket_policy" {
#   bucket = aws_s3_bucket.bucket.id

#   policy = var.policy != null ? var.policy : jsonencode({
#     Version = "2012-10-17",
#     Statement : []
#   })
# }

variable "bucket_name" {
  type        = string
  description = "The name of the S3 bucket"
}

variable "acl" {
  type        = string
  description = "The ACL for the S3 bucket"
}

variable "website" {
  type = object({
    index_document = string
    error_document = string
  })
  default     = null
  description = "Website configuration for S3 bucket"
}

variable "tags" {
  type        = map(string)
  description = "Tags to associate with the S3 bucket"
}

variable "policy" {
  type        = string
  default     = null
  description = "Policy JSON for the bucket"
}

# ---------------------------------------------
# WAF whitelist
# ---------------------------------------------
resource "aws_wafv2_ip_set" "ip_whitelist" {
  name               = "${local.name}-ip-whitelist"
  scope              = "REGIONAL"
  ip_address_version = "IPV4"

  # ソートして外から渡される配列要素の並び順が変わっても
  # リソースが再作成されないようにする。
  addresses = sort(var.waf_info.ip_whitelist)
}

# ---------------------------------------------
# WAF settings 
# ---------------------------------------------
resource "aws_wafv2_web_acl" "alb" {
  name        = "${local.name}-WebACL-webapi"
  description = "for fargate docker app"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "${local.name}-WebACL-webapi"
    sampled_requests_enabled   = true
  }

  # ---------------------------------------------
  # 通常ボットやその他の脅威に関連付けられている IP アドレスをブロックする場合に役立ちます。 ここの IP アドレスは AWS 管理です。
  rule {
    name     = "AWS-AWSManagedRulesAmazonIpReputationList"
    priority = 210

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesAmazonIpReputationList"
      sampled_requests_enabled   = true
    }
  }
  # -----------------------------------------------
  # Web アプリケーション防御の一般的なルールが含まれています。 OWASP 出版物や CVE で解説されている脆弱性を含んでいます。 AWS 管理のルールです。
  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 220

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  # ------------------------------------------------
  # Linux関連
  # https://dev.classmethod.jp/articles/awswaf-logcheck/
  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 230

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  # ------------------------------------------------
  # 既知の不正な入力マネージドルールグループ
  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 240

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  # ---------------------------------------------
  # ip-white list
  rule {
    name     = "${local.name}-rule-allow-ip-whitelist"
    priority = 250

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.ip_whitelist.arn
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "${local.name}-rule-allow-ip-whitelist"
      sampled_requests_enabled   = true
    }
  }

  # ------------------------------------------------
  # 管理者保護マネージドルールグループ
  rule {
    name     = "AWS-AWSManagedRulesAdminProtectionRuleSet"
    priority = 260

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAdminProtectionRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesAdminProtectionRuleSet"
      sampled_requests_enabled   = true
    }
  }
}

# ---------------------------------------------
# WAF settings confirm for attach ALB
# ---------------------------------------------
resource "aws_wafv2_web_acl_association" "alb" {
  count = length(var.waf_info.waf_target_arns)

  resource_arn = var.waf_info.waf_target_arns[count.index]
  web_acl_arn  = aws_wafv2_web_acl.alb.arn
}

variable "bucket_id" {
  type        = string
  description = "The id of the S3 bucket"
}

variable "bucket_arn" {
  type        = string
  description = "The ARN of the S3 bucket"
}

variable "bucket_domain" {
  type        = string
  description = "The domain name of the S3 bucket"
}

variable "default_ttl" {
  type        = number
  description = "The default TTL for CloudFront"
}

variable "max_ttl" {
  type        = number
  description = "The maximum TTL for CloudFront"
}

variable "min_ttl" {
  type        = number
  description = "The minimum TTL for CloudFront"
}

variable "tags" {
  type        = map(string)
  description = "Tags to associate with the CloudFront distribution"
}

variable "price_class" {
  type        = string
  description = "The price class for the CloudFront distribution (e.g. PriceClass_All, PriceClass_200, PriceClass_100)"
  default     = "PriceClass_100"
}

variable "restrictions" {
  type = object({
    geo_restriction = object({
      restriction_type = string
      locations        = list(string)
    })
  })
  description = "Restrictions for the CloudFront distribution"
  default = {
    geo_restriction = {
      restriction_type = "none"
      locations        = []
    }
  }
}

variable "try_files_arn" {
  type = string
}

variable "aliases" {
  type = list(string)
}

variable "acm_certificate_arn" {
  type        = string
  description = "The ARN of the ACM certificate"
}

variable "cloudfront_webacl_id" {
  type    = string
  default = null
}

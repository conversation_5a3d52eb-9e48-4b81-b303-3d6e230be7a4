provider "aws" {
  alias  = "useast1"
  region = "us-east-1" // ACM for CloudFront must be in us-east-1
}

# ACM
resource "aws_acm_certificate" "acm" {
  provider          = aws.useast1
  domain_name       = var.domain
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}

// register record for ACM
resource "aws_route53_record" "cert" {
  for_each = {
    for dvo in aws_acm_certificate.acm.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = var.zone_id
}

// kick off the validate process
resource "aws_acm_certificate_validation" "validation" {
  provider = aws.useast1
  timeouts {
    create = "5m"
  }
  certificate_arn         = aws_acm_certificate.acm.arn
  validation_record_fqdns = [for record in aws_route53_record.cert : record.fqdn]
}

resource "aws_route53_record" "record" {
  for_each = { for record in var.records : record.name => record }

  zone_id = var.zone_id
  name    = each.value.name
  type    = each.value.type

  alias {
    name                   = each.value.alias.name
    zone_id                = each.value.alias.zone_id
    evaluate_target_health = each.value.alias.evaluate_target_health
  }
}

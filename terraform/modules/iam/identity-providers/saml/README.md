# AWS IAM SAML Identity Provider Module

This Terraform module provides an IAM SAML identity provider in AWS

## Usage

```hcl
module "saml_provider" {
  source = "../modules/iam/identity-providers/saml"

  # Specify input variables here, if any
}
```

## Inputs
- `name`: Provider name
- `saml_metadata_document`: Metadata document (UTF-8 XML)

## Outputs
- `saml_provider_arn`: ARN assigned by AWS to this SAML identity provider

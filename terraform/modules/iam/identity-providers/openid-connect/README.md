# AWS IAM OpenID Connect Identity Provider Module

This Terraform module provides an IAM OpenID Connect identity provider in AWS

## Usage

```hcl
module "openid_connect_provider" {
  source = "../modules/iam/identity-providers/openid-connect"

  # Specify input variables here, if any
}
```

## Inputs
- `url`: Provider URL
- `client_id_list`: Client ID list
- `thumbprint_list`: Thumbprint list

## Outputs
- `openid_connect_provider_arn`: ARN assigned by AWS to this OpenID Connect identity provider

# AWS ECS Fargate Module

This Terraform module creates a Fargate ECS service in AWS

## Usage

```hcl
module "fargate" {
  source = "../modules/ecs-fargate/fargate"

  # Specify input variables here, if any
}
```

## Inputs
- `ecs_service_name`: Name of the service
- `cluster_id`: ARN of an ECS cluster
- `task_def_arn`: Full ARN of the task definition that you want to run in your service
- `desired_count`: Number of instances of the task definition to place and keep running
- `vpc_private_subnet_ids`: Subnets associated with the task or service
- `fargate_security_group_id`: Security groups associated with the task or service
- `alb_group_arn`: ARN of the Load Balancer target group to associate with the service
- `container_name`: Name of the container to associate with the load balancer
- `container_port`: Port on the container to associate with the load balancer

## Outputs
- (None)

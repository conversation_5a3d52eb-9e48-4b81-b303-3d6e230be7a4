# ECS
resource "aws_ecs_service" "ecs_service" {
  name                               = var.ecs_service_name
  cluster                            = var.cluster_id
  task_definition                    = var.task_def_arn
  launch_type                        = "FARGATE"
  desired_count                      = var.desired_count
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 10

  network_configuration {
    subnets          = var.vpc_private_subnet_ids
    security_groups  = [var.fargate_security_group_id]
    assign_public_ip = false # Providing our containers with public IPs
  }

  # https://docs.aws.amazon.com/AmazonECS/latest/developerguide/deployment-circuit-breaker.html
  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }

  lifecycle {
    ignore_changes = [task_definition]
  }

  dynamic "load_balancer" {
    for_each = var.alb_group_arn != "" ? [1] : []
    content {
      target_group_arn = var.alb_group_arn
      container_name   = var.container_name
      container_port   = var.container_port
    }
  }
}

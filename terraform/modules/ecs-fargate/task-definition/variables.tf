# Task
variable "task_family" {
  type        = string
  description = "A unique name for your task definition"
}

variable "cpu" {
  type        = string
  description = "Number of cpu units used by the task"
}

variable "memory" {
  type        = string
  description = "Amount (in MiB) of memory used by the task"
}

variable "execution_role_arn" {
  type        = string
  description = "ARN of the task execution role that the Amazon ECS container agent and the Dock<PERSON> daemon can assume"
}

variable "task_role_arn" {
  type        = string
  description = "ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services"
}

# Container definition
variable "ecr_url" {
  type        = string
  description = "The URL of the ECR repository"
}

variable "container_name" {
  type        = string
  description = "Name of the container definition"
}

variable "container_port" {
  type        = number
  description = "Port of the container definition"
}

# Log
variable "log_group" {
  type        = string
  description = "Name of the Log Group in CloudWatch Logs"
}

variable "log_region" {
  type        = string
  description = "AWS region where the Log Group and Log Streams will be created"
}

variable "log_stream_prefix" {
  type        = string
  description = "Prefix for the names of the Log Streams in CloudWatch Logs"
  default     = "ecs"
}

variable "environment" {
  type        = string
  description = "Environment definition"
  default     = "staging"
}

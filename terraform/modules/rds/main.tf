# Keypair
module "keypair" {
  source = "./keypair"

  key_name = "${var.rds_name}-bastion-host-pair"
}

# EC2 bastion
module "ec2_bastion" {
  source = "./ec2-bastion"

  instance_name        = "${var.rds_name}-bastion-host"
  key_name             = module.keypair.key_name
  vpc_id               = var.vpc_id
  cidr_blocks          = var.ec2_cidr_blocks
  vpc_public_subnet_id = var.vpc_public_subnet_id
}

# RDS
module "rds" {
  source = "./rds"

  rds_name                   = var.rds_name
  engine_version             = var.engine_version
  private_rds_subnet_ids     = var.private_rds_subnet_ids
  db_name                    = var.db_name
  db_username                = var.db_username
  rds_instance               = var.rds_instance
  rds_port                   = var.rds_port
  ingress_security_group_ids = [module.ec2_bastion.security_group_id]
  vpc_id                     = var.vpc_id
  allow_inbound_sg_ids       = var.allow_inbound_sg_ids
  multi_az                   = var.multi_az
}

# Secrets
module "secrets" {
  source = "./secrets"

  environment     = var.environment
  db_name         = var.db_name
  db_username     = var.db_username
  db_address      = module.rds.db_address
  db_password     = module.rds.db_password
  bastion_ip      = module.ec2_bastion.public_ip
  private_key_pem = module.keypair.private_key_pem
}

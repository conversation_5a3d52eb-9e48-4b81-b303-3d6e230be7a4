resource "aws_secretsmanager_secret" "secret_rds" {
  name = "${var.environment}-secret-rds"
}

locals {
  keypem = replace(var.private_key_pem, "\n", "\\n")
}

resource "aws_secretsmanager_secret_version" "secret_credentials" {
  secret_id     = aws_secretsmanager_secret.secret_rds.id
  secret_string = <<EOF
{
  "SECRET_POSTGRES_HOSTNAME": "${var.db_address}",
  "SECRET_POSTGRES_DATABASE": "${var.db_name}",
  "SECRET_POSTGRES_PORT": "5432",
  "SECRET_POSTGRES_PASSWORD": "${var.db_password}",
  "SECRET_POSTGRES_USER": "${var.db_username}",
  "SECRET_EC2_PEM": "${local.keypem}",
  "EC2_IP": "${var.bastion_ip}"
}
EOF
  lifecycle {
    ignore_changes = [
      secret_string,
      version_stages,
    ]
  }
}

variable "rds_name" {
  type        = string
  description = "RDS name"
}

variable "private_rds_subnet_ids" {
  type        = list(string)
  description = "A list of VPC subnet IDs"
}

variable "db_name" {
  type        = string
  description = "The name of the database to create when the DB instance is created"
}

variable "db_username" {
  type        = string
  description = "Username for the master DB user"
}

variable "rds_instance" {
  type        = string
  description = "The RDS instance class"
}

variable "rds_port" {
  type        = string
  description = "The database port"
  default     = "5432"
}

variable "engine_version" {
  type        = string
  description = "The engine version to use"
  default     = "14.8"
}

variable "ingress_security_group_ids" {
  type        = list(string)
  description = "List of VPC security groups ingress"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID"
}

variable "allow_inbound_sg_ids" {
  type        = list(string)
  description = "List of security groups ingress"
}

variable "multi_az" {
  type        = bool
  description = "Specifies if the RDS instance is multi-AZ"
  default     = true
}

variable "rds_name" {
  type        = string
  description = "RDS name"
}

variable "environment" {
  type        = string
  description = "environment definition"
  default     = "dev"
}

# VPC
variable "vpc_id" {
  type        = string
  description = "VPC ID"
}

# ECS bastion
variable "ec2_cidr_blocks" {
  type        = list(string)
  description = "List of CIDR blocks access to EC2 Bastion host"
  default     = ["0.0.0.0/0"]
}

variable "vpc_public_subnet_id" {
  type        = string
  description = "VPC Public Subnet ID to launch in"
}

variable "private_rds_subnet_ids" {
  type        = list(string)
  description = "A list of VPC Private Subnet IDs"
}

variable "db_name" {
  type        = string
  description = "The name of the database to create when the DB instance is created"
}

variable "db_username" {
  type        = string
  description = "Username for the master DB user"
  default     = "postgres"
}

variable "rds_instance" {
  type        = string
  description = "Name of EC2 instance"
  default     = "db.t3.micro"
}

variable "rds_port" {
  type        = number
  description = "The database port"
  default     = 5432
}

variable "engine_version" {
  type        = string
  description = "The engine version to use"
  default     = "14.8"
}

variable "allow_inbound_sg_ids" {
  type        = list(string)
  description = "List of security groups ingress"
}

variable "multi_az" {
  type        = bool
  description = "Specifies if the RDS instance is multi-AZ"
  default     = true
}

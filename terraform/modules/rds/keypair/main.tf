# keypair
resource "tls_private_key" "rsa" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_key_pair" "bastion_rds_pair" {
  key_name   = var.key_name
  public_key = tls_private_key.rsa.public_key_openssh
  lifecycle {
    ignore_changes = [public_key]
  }
}
# Issue_0243　This change is unnecessary and will be commented for environmental consistency. Uncomment if necessary.
/*
resource "local_file" "tf_key" {
  content  = tls_private_key.rsa.private_key_pem
  filename = "${var.key_name}.pem"
}
*/

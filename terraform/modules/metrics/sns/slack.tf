resource "aws_iam_role" "lambda_role" {
  name = "sns-to-slack-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "basic_execution" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# resource "aws_iam_policy" "lambda_policy" {
#   name = "sns-to-slack-lambda-policy"

#   policy = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Effect = "Allow"
#         Action = [
#           "sns:Publish",
#           "sns:Subscribe",
#           "logs:CreateLogGroup",
#           "logs:CreateLogStream",
#           "logs:PutLogEvents"
#         ]
#         Resource = "*"
#       }
#     ]
#   })
# }

# resource "aws_iam_role_policy_attachment" "lambda_policy_attach" {
#   role       = aws_iam_role.lambda_role.name
#   policy_arn = aws_iam_policy.lambda_policy.arn
# }

data "archive_file" "lambda_package" {
  type        = "zip"
  source_file = var.source_file
  output_path = "lambda.zip"
}

resource "aws_lambda_function" "sns_to_slack" {
  function_name = "sns-to-slack"
  runtime       = "python3.8"
  handler       = "lambda_function.sns_to_slack"
  role          = aws_iam_role.lambda_role.arn
  filename      = data.archive_file.lambda_package.output_path

  environment {
    variables = {
      SLACK_WEBHOOK_URL = var.slack_webhook_url
    }
  }
}

resource "aws_lambda_permission" "allow_sns" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sns_to_slack.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.cloudwatch_updates.arn
}

resource "aws_sns_topic_subscription" "sns_lambda" {
  topic_arn = aws_sns_topic.cloudwatch_updates.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.sns_to_slack.arn
}


### HealthyHostCount - Healthy instance count - Alert when 0, in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "alb_healthy_count" {
  alarm_name          = "${var.alb_arn_suffix}_healthy_count_low"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "HealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = var.statistic_period
  statistic           = "Maximum"
  threshold           = 1
  alarm_description   = "No healthy instance over last 5 minutes"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    "LoadBalancer" = var.alb_arn_suffix
    "TargetGroup"  = var.tg_arn_suffix
  }
}

### HTTPCode_ELB_5XX_Count - 500+ errors count - Alert when above 100, in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "http_5xx_count" {
  alarm_name          = "${var.alb_arn_suffix}_5xx_count"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "HTTPCode_ELB_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = var.statistic_period
  statistic           = "Sum"
  threshold           = 100
  alarm_description   = "No healthy instance over last 5 minutes"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    "LoadBalancer" = var.alb_arn_suffix
  }
}

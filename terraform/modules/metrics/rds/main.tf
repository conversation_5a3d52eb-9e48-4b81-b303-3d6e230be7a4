resource "aws_cloudwatch_metric_alarm" "rds_cpu_utilization_high" {
  alarm_name          = "rds_cpu_utilization_high"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = var.statistic_period
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Average database CPU utilization over last 5 minutes too high"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    DBInstanceIdentifier = var.rds_id
  }
}

### Memory utilization - Alert when 90%+ in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "rds_memory_utilization_high" {
  alarm_name          = "rds_memory_utilization_high"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "FreeableMemory"
  namespace           = "AWS/RDS"
  period              = var.statistic_period
  statistic           = "Average"
  threshold           = var.rds_freeable_memory_threshold
  alarm_description   = "Average database Freeable Memory over last 5 minutes too low"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    DBInstanceIdentifier = var.rds_id
  }
}

### Disk utilization - Alert when 60%+ in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "rds_disk_utilization_high" {
  alarm_name          = "rds_disk_utilization_high"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "FreeStorageSpace"
  namespace           = "AWS/RDS"
  period              = var.statistic_period
  statistic           = "Average"
  threshold           = var.rds_free_storage_space_threshold
  alarm_description   = "Average database Freeable Storage over last 5 minutes too high"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    DBInstanceIdentifier = var.rds_id
  }
}

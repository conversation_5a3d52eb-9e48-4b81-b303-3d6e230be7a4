.PHONY: lint test

LINT := tflint --config "$(PWD)/.tflint.hcl"
SEC  := tfsec --minimum-severity HIGH

lint:
	tflint --init

	@echo
	@echo "Linting environments.."
	find ./envs/* -type d | xargs -I{} $(LINT) --chdir {} || true

	@echo
	@echo "Linting modules.."
	find ./modules/* \
		-type d \
		| xargs -I{} bash -c 'echo {} && $(LINT) --chdir {} --force'

	@echo
	@echo "Running security checks.."
	tfsec --minimum-severity HIGH ./modules

format:
	terraform fmt -recursive

# Used for CI consistency, do not remove.
test: lint

# Security Group
module "alb_api_security_group" {
  source = "../../modules/security-group"

  ingress_ports       = ["443", "80"]
  ingress_protocol    = "tcp"
  egress_ports        = ["0"]
  egress_protocol     = "-1"
  vpc_id              = module.vpc.vpc_id
  sg_name             = "${var.project_name}-${var.environment}-api-alb-sg"
  egress_cidr_blocks  = ["0.0.0.0/0"]
  ingress_cidr_blocks = ["0.0.0.0/0"]
}

# ELB
module "alb_api" {
  source = "../../modules/elb"

  lb_name              = "${var.project_name}-${var.environment}-${var.lb_api_name}"
  lb_type              = var.lb_type
  public_subnet_ids    = module.vpc.public_subnet_ids
  lb_target_group_name = "${var.project_name}-${var.environment}-${var.tg_api_name}"
  lb_target_port       = var.api_port
  vpc_id               = module.vpc.vpc_id
  zone_id              = var.zone_id
  domain               = local.api_domain
  route53_record_name  = var.api_route53_record_name
  security_group_ids   = [module.alb_api_security_group.security_group_id]
}

# CloudFront for Deployment Files (User)
resource "aws_cloudfront_function" "try_files" {
  name    = "tryFilesFunction"
  runtime = "cloudfront-js-1.0"
  comment = "try_files"

  code = <<EOT
function handler(event) {
    var request = event.request;
    var headers = request.headers;
    
    var validAuth = "Basic YWRtaW46cGFzc3dvcmQxMjM=";
    if (!headers.authorization || headers.authorization.value !== validAuth) {
        return {
            statusCode: 401,
            statusDescription: "Unauthorized",
            headers: {
                "www-authenticate": { value: "Basic realm='Secure Area'" }
            },
            body: "Unauthorized"
        };
    }
    
    var uri = request.uri;
    if (uri.endsWith("/")) {
        request.uri += "index.html";
        return request;
    }

    var fileExt = uri.split('.').pop();
    if (!["html", "css", "js", "png", "jpg", "gif", "json", "tff", "otf", "wasm", "bin"].includes(fileExt)) {
        request.uri = "/index.html";
    }

    return request;
}
EOT
}

module "cloudfront_webapp_user" {
  source = "../../modules/cloudfront"

  bucket_id            = module.s3_webapp_user_deployment.bucket_id
  bucket_arn           = module.s3_webapp_user_deployment.bucket_arn
  bucket_domain        = module.s3_webapp_user_deployment.bucket_domain
  try_files_arn        = null
  aliases              = [local.webapp_user_domain]
  acm_certificate_arn  = module.route53_webapp_user.acm_certificate_arn
  cloudfront_webacl_id = module.gl_secure.cloudfront_webacl_id

  default_ttl = 300
  min_ttl     = 0
  max_ttl     = 86400

  tags = {
    Environment = var.environment
    Project     = var.project_name
    Purpose     = "webapp user cloudfront"
  }
}

# CloudFront for Deployment Files (Admin)
module "cloudfront_webapp_admin" {
  source = "../../modules/cloudfront"

  bucket_id            = module.s3_webapp_admin_deployment.bucket_id
  bucket_arn           = module.s3_webapp_admin_deployment.bucket_arn
  bucket_domain        = module.s3_webapp_admin_deployment.bucket_domain
  try_files_arn        = aws_cloudfront_function.try_files.arn
  aliases              = [local.webapp_admin_domain]
  acm_certificate_arn  = module.route53_webapp_admin.acm_certificate_arn
  cloudfront_webacl_id = module.gl_secure.cloudfront_webacl_id

  default_ttl = 3600
  min_ttl     = 0
  max_ttl     = 86400

  tags = {
    Environment = var.environment
    Project     = var.project_name
    Purpose     = "webapp admin cloudfront"
  }
}

# CloudFront for CDN User Upload File
module "cloudfront_cdn_user_upload" {
  source = "../../modules/cloudfront"

  bucket_id            = module.s3_upload_files.bucket_id
  bucket_arn           = module.s3_upload_files.bucket_arn
  bucket_domain        = module.s3_upload_files.bucket_domain
  try_files_arn        = null
  aliases              = [local.cdn_domain]
  acm_certificate_arn  = module.route53_cdn.acm_certificate_arn
  cloudfront_webacl_id = module.gl_secure.cloudfront_webacl_id

  default_ttl = 3600
  min_ttl     = 0
  max_ttl     = 86400

  tags = {
    Environment = var.environment
    Project     = var.project_name
    Purpose     = "cdn user upload cloudfront"
  }
}

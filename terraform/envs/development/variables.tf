# prerequisite value
variable "aws_account_id" {
  description = "The AWS Account ID."
  type        = string
  default     = "************"
}

variable "zone_id" {
  type    = string
  default = "Z06942402HWSYH509SBS5"
}

variable "zone_name" {
  type    = string
  default = "dtnewec.com"
}

variable "sns_endpoint" {
  type    = list(string)
  default = ["<EMAIL>"]
}

# local
locals {
  sm_readonly = file("./policies/sm-readonly.json")
  github_cd = templatefile("./policies/github-cd.json", {
    aws_region                             = var.aws_region,
    aws_account_id                         = var.aws_account_id,
    project_name                           = var.project_name,
    environment                            = var.environment,
    s3_bucket_name_webapp_user_deployment  = local.s3_bucket_name_webapp_user_deployment,
    s3_bucket_name_webapp_admin_deployment = local.s3_bucket_name_webapp_admin_deployment
  })
  github_oidc = templatefile("./policies/github-oidc.json", {
    aws_account_id    = var.aws_account_id,
    aws_sts_client_id = var.aws_sts_client_id,
    github_repository = var.github_repository
  })
  s3_user_upload_file = templatefile("./policies/s3-upload.json", {
    s3_bucket_name_user_upload_file = local.s3_bucket_name_user_upload_file
  })
}

locals {
  waf_ip_whitelist = ["*************/32"]
}

# common
variable "environment" {
  type        = string
  description = "environment definition"
  default     = "dev"
}


variable "aws_region" {
  type        = string
  description = "name region, default is Tokyo"
  default     = "ap-northeast-1"
}

variable "project_name" {
  type        = string
  description = "project name"
  default     = "as"
}

variable "github_repository" {
  type        = string
  description = "github repository"
  default     = "Market-place-service-AS/market-place-service-AS"
}

# AZ count
variable "az_count" {
  type        = number
  description = "num of AZs"
  default     = 2
}

# VPC
variable "vpc_cidr" {
  type        = string
  description = "vpc cidr block"
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidr" {
  type        = list(string)
  description = "public subnet cidr block"
  default = [
    "10.0.0.0/24",
    "********/24"
  ]
}

variable "private_subnet_cidr" {
  type        = list(string)
  description = "private subnet cidr block"
  default = [
    "********/24",
    "********/24"
  ]
}

# ECR
variable "ecr_api_repository" {
  type        = string
  description = "api repository name"
  default     = "api"
}

variable "ecr_worker_repository" {
  type        = string
  description = "worker repository name"
  default     = "worker"
}

variable "ecs_cluster" {
  type    = string
  default = "ecs-cluster"
}

# TaskDefinition
variable "api_task_family" {
  type    = string
  default = "api"
}

variable "worker_task_family" {
  type    = string
  default = "worker"
}

variable "api_ecs_service" {
  type    = string
  default = "api"
}

variable "worker_ecs_service" {
  type    = string
  default = "worker"
}

variable "api_port" {
  type    = number
  default = 3000
}

variable "api_container_name" {
  type    = string
  default = "api"
}

variable "worker_container_name" {
  type    = string
  default = "worker"
}

variable "api_desired_count" {
  type    = number
  default = 1
}

variable "api_cpu" {
  type    = number
  default = 1024
}

variable "api_memory" {
  type    = number
  default = 2048
}

variable "worker_desired_count" {
  type    = number
  default = 1
}

variable "worker_cpu" {
  type    = number
  default = 512
}

variable "worker_memory" {
  type    = number
  default = 1024
}

# CloudWatch Logs
variable "api_log_group_name" {
  type    = string
  default = "api"
}

variable "worker_log_group_name" {
  type    = string
  default = "worker"
}

variable "log_stream_name" {
  type    = string
  default = "ecs"
}

# ELB
variable "lb_type" {
  type    = string
  default = "application"
}

variable "lb_api_name" {
  type    = string
  default = "api"
}

variable "tg_api_name" {
  type    = string
  default = "api"
}

variable "api_route53_record_name" {
  type    = string
  default = "api.dev"
}

variable "webapp_user_route53_record_name" {
  type    = string
  default = "dev"
}

variable "webapp_admin_route53_record_name" {
  type    = string
  default = "admin.dev"
}

variable "cdn_route53_record_name" {
  type    = string
  default = "cdn.dev"
}

# EC2 Bastion
variable "ec2_cidr_blocks" {
  description = "define cidr blocks that can access to bastion host"
  type        = list(string)
  default     = ["0.0.0.0/0"]
  # default     = ["*************/32"] # allow ssh from CCL to Bastion server
}

# RDS
variable "db_name" {
  type    = string
  default = "asdev"
}

variable "db_username" {
  type    = string
  default = "postgres"
}

variable "rds_instance" {
  type    = string
  default = "db.t3.micro"
}

variable "rds_port" {
  type        = number
  description = "This configures a port number of rds"
  default     = 5432
}

variable "rds_engine_version" {
  type        = string
  description = "The engine version to use"
  default     = "16.3"
}

variable "rds_multi_az" {
  type        = bool
  description = "Specifies if the RDS instance is multi-AZ"
  default     = false
}

# IAM Identity Provider
variable "aws_sts_client_id" {
  type    = string
  default = "sts.amazonaws.com"
}

variable "github_actions_oidc_url" {
  type    = string
  default = "https://token.actions.githubusercontent.com"
}

variable "github_actions_oidc_thumbprints" {
  type    = list(string)
  default = ["6938fd4d98bab03faadb97b34396831e3780aea1", "1c58a3a8518e8759bf075b76b750d4f2df264fcd"]
}

# S3
locals {
  s3_bucket_name_webapp_user_deployment  = "${var.project_name}-web-user-${var.environment}"
  s3_bucket_name_webapp_admin_deployment = "${var.project_name}-web-admin-${var.environment}"
  s3_bucket_name_user_upload_file        = "${var.project_name}-user-upload-${var.environment}"
}

# Route53
locals {
  api_domain          = "${var.api_route53_record_name}.${var.zone_name}"
  webapp_user_domain  = var.webapp_user_route53_record_name == "" ? var.zone_name : "${var.webapp_user_route53_record_name}.${var.zone_name}"
  webapp_admin_domain = "${var.webapp_admin_route53_record_name}.${var.zone_name}"
  cdn_domain          = "${var.cdn_route53_record_name}.${var.zone_name}"
}

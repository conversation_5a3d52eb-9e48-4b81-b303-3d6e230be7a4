# Route53 for CloudFront (User)
module "route53_webapp_user" {
  source = "../../modules/route53"

  zone_id = var.zone_id
  domain  = local.webapp_user_domain
  records = [
    {
      name = local.webapp_user_domain
      type = "A"
      alias = {
        name                   = module.cloudfront_webapp_user.domain_name
        zone_id                = module.cloudfront_webapp_user.hosted_zone_id
        evaluate_target_health = false
      }
    }
  ]
}

# Route53 for CloudFront (Admin)
module "route53_webapp_admin" {
  source = "../../modules/route53"

  zone_id = var.zone_id
  domain  = local.webapp_admin_domain
  records = [
    {
      name = local.webapp_admin_domain
      type = "A"
      alias = {
        name                   = module.cloudfront_webapp_admin.domain_name
        zone_id                = module.cloudfront_webapp_admin.hosted_zone_id
        evaluate_target_health = false
      }
    }
  ]
}

# Route53 for CloudFront (CDN User Upload)
module "route53_cdn" {
  source = "../../modules/route53"

  zone_id = var.zone_id
  domain  = local.cdn_domain
  records = [
    {
      name = local.cdn_domain
      type = "A"
      alias = {
        name                   = module.cloudfront_cdn_user_upload.domain_name
        zone_id                = module.cloudfront_cdn_user_upload.hosted_zone_id
        evaluate_target_health = false
      }
    }
  ]
}

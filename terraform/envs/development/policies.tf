module "sm_readonly" {
  source = "../../modules/iam/policies"

  policy_name      = "SecretsManagerReadOnly"
  policy_statement = local.sm_readonly
}

module "github_cd" {
  source = "../../modules/iam/policies"

  policy_name      = "GithubCD"
  policy_statement = local.github_cd
}

module "s3_user_upload_file" {
  source = "../../modules/iam/policies"

  policy_name      = "S3UserUploadFile"
  policy_statement = local.s3_user_upload_file
}

import json
import urllib3
import os

http = urllib3.PoolManager()
SLACK_WEBHOOK_URL = os.environ['SLACK_WEBHOOK_URL']

def sns_to_slack(event, context):
    # Get SNS message
    sns_message = json.loads(event['Records'][0]['Sns']['Message'])
    
    # Get necessary information
    alarm_name = sns_message.get("AlarmName", "Unknown Alarm")
    new_state = sns_message.get("NewStateValue", "UNKNOWN")
    reason = sns_message.get("NewStateReason", "No reason provided")
    region = sns_message.get("Region", "Unknown Region")
    timestamp = sns_message.get("StateChangeTime", "Unknown Time")

    # Choose color based on alarm state
    color = "#ff0000" if new_state == "ALARM" else "#36a64f"

    # Format Block Kit to send to Slack
    slack_payload = {
        "attachments": [
            {
                "color": color,
                "blocks": [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            "text": f"🚨 AWS Alarm: {alarm_name}"
                        }
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": f"*State:* `{new_state}`"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Region:* `{region}`"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Time:* `{timestamp}`"
                            }
                        ]
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Reason:*\n>{reason}"
                        }
                    }
                ]
            }
        ]
    }

    # Send data to Slack
    response = http.request(
        "POST",
        SLACK_WEBHOOK_URL,
        body=json.dumps(slack_payload),
        headers={"Content-Type": "application/json"}
    )

    return {
        "statusCode": response.status,
        "body": response.data.decode('utf-8')
    }

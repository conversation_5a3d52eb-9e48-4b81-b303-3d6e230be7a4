# Market Place Service Development Environment
Infra as code with terraform for development environment of Market Place Service project

## Project: Market Place Service

## Description
### AWS Network Configuration: Amazon VPC
- VPC CIDR: 10.0.0.0/16, providing a logically isolated section of the AWS Cloud
- Subnets:
  - Public Subnets: Two subnets (10.0.0.0/24, ********/24) with access to the Internet through an Internet Gateway.
  - Private Subnets: Two subnets (********/24, ********/24) use a NAT Gateway for outbound traffic to the Internet, with no inbound Internet access.
- Internet Gateway: Facilitates online communication for the VPC.
- NAT Gateway: Enables instances in private subnets to initiate outbound Internet traffic.

### Container Management
- Amazon ECR:
  - API (Go)
- Amazon ECS (Fargate):
  - API: Deploys 2 tasks in private subnets for resilience, high availability.

### WebApp Hosting
- Amazon S3:
  - Hosts the static assets (HTML, CSS, JS) of the Flutter WebApp.
  - Configured for static website hosting with index.html as the entry point and error fallback.
- Amazon CloudFront:
  - Configured as a Content Delivery Network (CDN) for the S3 bucket.
  - Provides global caching and HTTPS support.
  - Routes all traffic to index.html for client-side routing support.

### Connectivity
- Application Load Balancer
  - 1 ALB for route traffic to API target group.
  - 1 ALB to route traffic to the WebApp hosted on S3.

### Security and Monitoring
- AWS Secret Manager: Store environments of Application.
- CloudWatch Logs: Monitor log from ECS services

## Setup
```bash
export AWS_ACCESS_KEY_ID=*****
export AWS_SECRET_ACCESS_KEY=*****
export AWS_REGION=ap-northeast-1

make pull
make plan target=xxx
make apply target=xxx
```

## Provision resources
Make sure to provision resources in the following orders.
1. vpc
2. rds
3. ecr
4. policies
5. alb
6. ecs-fargate
7. s3
8. cloudfront

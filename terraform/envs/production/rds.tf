module "rds" {
  source = "../../modules/rds"

  rds_name               = "${var.project_name}-${var.environment}-rds"
  environment            = var.environment
  vpc_id                 = module.vpc.vpc_id
  ec2_cidr_blocks        = var.ec2_cidr_blocks
  vpc_public_subnet_id   = module.vpc.public_subnet_ids[0]
  private_rds_subnet_ids = module.vpc.private_subnet_ids
  db_name                = var.db_name
  db_username            = var.db_username
  rds_instance           = var.rds_instance
  rds_port               = var.rds_port
  engine_version         = var.rds_engine_version
  multi_az               = var.rds_multi_az
  allow_inbound_sg_ids   = [module.api_fargate_security_group.security_group_id, module.worker_fargate_security_group.security_group_id]
}

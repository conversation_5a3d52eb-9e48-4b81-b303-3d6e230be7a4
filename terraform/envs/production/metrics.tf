### SNS
module "sns_alert" {
  source = "../../modules/metrics/sns"

  sns_endpoint = var.sns_endpoint
  source_file  = "lambda_functions/lambda_function.py"
}

### RDS metric
module "rds_metric" {
  source = "../../modules/metrics/rds"

  sns_topic_arn = module.sns_alert.sns_topic_arn
  rds_id        = module.rds.rds_id
}

### ECS API metric
module "api_metric" {
  source = "../../modules/metrics/ecs"

  sns_topic_arn    = module.sns_alert.sns_topic_arn
  ecs_service_name = module.api_fargate.service_name
  ecs_cluster_name = module.ecs_cluster.cluster_name
}

### ALB metric for API
module "api_alb_metric" {
  source = "../../modules/metrics/alb"

  sns_topic_arn  = module.sns_alert.sns_topic_arn
  alb_arn_suffix = module.alb_api.aws_lb_arn_suffix
  tg_arn_suffix  = module.alb_api.alb_tg_arn_suffix
}

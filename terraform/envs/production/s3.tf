# S3 Bucket for Deployment Files (User)
module "s3_webapp_user_deployment" {
  source = "../../modules/s3"

  bucket_name = local.s3_bucket_name_webapp_user_deployment
  acl         = "private"
  website = {
    index_document = "index.html"
    error_document = "index.html"
  }
  tags = {
    Environment = var.environment
    Project     = var.project_name
    Purpose     = "webapp user deployment"
  }
}

# S3 Bucket for Deployment Files (Admin)
module "s3_webapp_admin_deployment" {
  source = "../../modules/s3"

  bucket_name = local.s3_bucket_name_webapp_admin_deployment
  acl         = "private"
  website = {
    index_document = "index.html"
    error_document = "index.html"
  }
  tags = {
    Environment = var.environment
    Project     = var.project_name
    Purpose     = "webapp admin deployment"
  }
}

# S3 Bucket for upload files
module "s3_upload_files" {
  source = "../../modules/s3"

  bucket_name = local.s3_bucket_name_user_upload_file
  acl         = "private"

  tags = {
    Environment = var.environment
    Project     = var.project_name
    Purpose     = "webapp user upload"
  }
}

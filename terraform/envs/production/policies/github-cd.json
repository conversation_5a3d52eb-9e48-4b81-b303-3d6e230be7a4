{"Version": "2012-10-17", "Statement": [{"Sid": "VisualEditor1", "Effect": "Allow", "Action": "secretsmanager:GetSecretValue", "Resource": ["*"]}, {"Sid": "VisualEditor2", "Effect": "Allow", "Action": "ecr:GetAuthorizationToken", "Resource": ["*"]}, {"Sid": "VisualEditor3", "Effect": "Allow", "Action": ["ecr:CompleteLayerUpload", "ecr:UploadLayerPart", "ecr:InitiateLayerUpload", "ecr:BatchCheckLayerAvailability", "ecr:PutImage", "ecr:DescribeRepositories"], "Resource": ["arn:aws:ecr:${aws_region}:${aws_account_id}:repository/${project_name}-${environment}-api", "arn:aws:ecr:${aws_region}:${aws_account_id}:repository/${project_name}-${environment}-worker"]}, {"Sid": "VisualEditor4", "Effect": "Allow", "Action": ["ecs:DescribeTaskDefinition", "ecs:RegisterTaskDefinition"], "Resource": ["*"]}, {"Sid": "VisualEditor5", "Effect": "Allow", "Action": ["ecs:UpdateService", "ecs:DescribeServices"], "Resource": ["arn:aws:ecs:${aws_region}:${aws_account_id}:service/${project_name}-${environment}-ecs-cluster/${project_name}-${environment}-api", "arn:aws:ecs:${aws_region}:${aws_account_id}:service/${project_name}-${environment}-ecs-cluster/${project_name}-${environment}-worker"]}, {"Sid": "VisualEditor6", "Effect": "Allow", "Action": ["iam:PassRole"], "Resource": ["arn:aws:iam::${aws_account_id}:role/${project_name}-${environment}-api-ecsTaskRole", "arn:aws:iam::${aws_account_id}:role/${project_name}-${environment}-worker-ecsTaskRole", "arn:aws:iam::${aws_account_id}:role/${project_name}-${environment}-ecsTaskExecutionRole"]}, {"Sid": "VisualEditor7", "Effect": "Allow", "Action": ["s3:ListBucket"], "Resource": ["arn:aws:s3:::${s3_bucket_name_webapp_user_deployment}", "arn:aws:s3:::${s3_bucket_name_webapp_admin_deployment}"]}, {"Sid": "VisualEditor8", "Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"], "Resource": ["arn:aws:s3:::${s3_bucket_name_webapp_user_deployment}/*", "arn:aws:s3:::${s3_bucket_name_webapp_admin_deployment}/*"]}, {"Sid": "VisualEditor9", "Effect": "Allow", "Action": ["cloudfront:CreateInvalidation"], "Resource": "*"}]}
.PHONY: init plan apply pull format lint

TF_PREFIX := -lock=false

TF := $(TF_PREFIX) terraform

ifeq ("$(target)","")
TF_SUFFIX := -lock=false
else
TF_SUFFIX := -target=$(target)
endif

init:
	@echo "Initiating Terraform.."
	@$(TF) init

plan:
	@$(TF) plan $(TF_SUFFIX) -out tf.plan

apply:
	@$(TF) apply -auto-approve $(TF_SUFFIX) tf.plan

pull:
	@$(TF) state pull > terraform.tfstate

format:
	terraform fmt -recursive

lint:
	cd ../../ && make lint
